/**
 * Visual Editors Test Utilities
 * Provides utilities for testing visual editor components
 */

import { vi } from 'vitest';

export interface MockEditorState {
  selectedTool: string;
  activeLayer: string;
  zoom: number;
  pan: { x: number; y: number };
  history: any[];
  isDirty: boolean;
}

export interface MockAsset {
  id: string;
  name: string;
  type: 'texture' | 'material' | 'model' | 'light';
  url: string;
  metadata: Record<string, any>;
}

export interface MockScene {
  id: string;
  name: string;
  assets: MockAsset[];
  lights: any[];
  cameras: any[];
  objects: any[];
}

/**
 * Create mock editor state
 */
export function createMockEditorState(overrides: Partial<MockEditorState> = {}): MockEditorState {
  return {
    selectedTool: 'select',
    activeLayer: 'default',
    zoom: 1.0,
    pan: { x: 0, y: 0 },
    history: [],
    isDirty: false,
    ...overrides,
  };
}

/**
 * Create mock asset
 */
export function createMockAsset(overrides: Partial<MockAsset> = {}): MockAsset {
  const id = `asset-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  return {
    id,
    name: `Test Asset ${id}`,
    type: 'texture',
    url: `https://example.com/assets/${id}.jpg`,
    metadata: {
      width: 1024,
      height: 1024,
      format: 'jpg',
      size: 512000,
    },
    ...overrides,
  };
}

/**
 * Create mock scene
 */
export function createMockScene(overrides: Partial<MockScene> = {}): MockScene {
  const id = `scene-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  return {
    id,
    name: `Test Scene ${id}`,
    assets: [createMockAsset({ type: 'texture' }), createMockAsset({ type: 'material' })],
    lights: [
      {
        id: 'light-1',
        type: 'directional',
        position: { x: 0, y: 10, z: 0 },
        intensity: 1.0,
        color: '#ffffff',
      },
    ],
    cameras: [
      {
        id: 'camera-1',
        type: 'perspective',
        position: { x: 0, y: 0, z: 10 },
        target: { x: 0, y: 0, z: 0 },
        fov: 45,
      },
    ],
    objects: [
      {
        id: 'object-1',
        type: 'mesh',
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      },
    ],
    ...overrides,
  };
}

/**
 * Create mock Vue component instance
 */
export function createMockVueComponent(componentName: string, props: Record<string, any> = {}) {
  return {
    $props: props,
    $data: {},
    $refs: {},
    $emit: vi.fn(),
    $nextTick: vi.fn().mockResolvedValue(undefined),
    $forceUpdate: vi.fn(),
    $destroy: vi.fn(),
    $mount: vi.fn(),
    $el: document.createElement('div'),

    // Component-specific methods based on name
    ...(componentName === 'ShowroomLayoutEditor' && {
      addObject: vi.fn(),
      removeObject: vi.fn(),
      selectObject: vi.fn(),
      updateObjectPosition: vi.fn(),
      saveLayout: vi.fn(),
      loadLayout: vi.fn(),
    }),

    ...(componentName === 'ProductConfigurator' && {
      addConfiguration: vi.fn(),
      removeConfiguration: vi.fn(),
      updateConfiguration: vi.fn(),
      validateConfiguration: vi.fn(),
      exportConfiguration: vi.fn(),
    }),

    ...(componentName === 'MaterialTextureEditor' && {
      loadTexture: vi.fn(),
      applyTexture: vi.fn(),
      adjustProperties: vi.fn(),
      previewMaterial: vi.fn(),
      saveMaterial: vi.fn(),
    }),

    ...(componentName === 'LightingEditor' && {
      addLight: vi.fn(),
      removeLight: vi.fn(),
      updateLightProperties: vi.fn(),
      previewLighting: vi.fn(),
      saveLightingSetup: vi.fn(),
    }),

    ...(componentName === 'AnimationEditor' && {
      createKeyframe: vi.fn(),
      deleteKeyframe: vi.fn(),
      playAnimation: vi.fn(),
      pauseAnimation: vi.fn(),
      exportAnimation: vi.fn(),
    }),
  };
}

/**
 * Create mock API client
 */
export function createMockApiClient() {
  return {
    get: vi.fn().mockResolvedValue({
      data: {
        data: [],
        success: true,
      },
    }),

    post: vi.fn().mockResolvedValue({
      data: {
        data: {},
        success: true,
      },
    }),

    patch: vi.fn().mockResolvedValue({
      data: {
        data: {},
        success: true,
      },
    }),

    delete: vi.fn().mockResolvedValue({
      data: {
        success: true,
      },
    }),

    put: vi.fn().mockResolvedValue({
      data: {
        data: {},
        success: true,
      },
    }),
  };
}

/**
 * Create mock API responses
 */
export function createMockApiResponses() {
  return {
    getAssets: vi.fn().mockResolvedValue({
      data: [
        createMockAsset({ type: 'texture' }),
        createMockAsset({ type: 'material' }),
        createMockAsset({ type: 'model' }),
      ],
      success: true,
    }),

    saveAsset: vi.fn().mockResolvedValue({
      data: createMockAsset(),
      success: true,
    }),

    deleteAsset: vi.fn().mockResolvedValue({
      success: true,
    }),

    getScene: vi.fn().mockResolvedValue({
      data: createMockScene(),
      success: true,
    }),

    saveScene: vi.fn().mockResolvedValue({
      data: createMockScene(),
      success: true,
    }),

    validateScene: vi.fn().mockResolvedValue({
      data: {
        valid: true,
        errors: [],
        warnings: [],
      },
      success: true,
    }),
  };
}

/**
 * Create mock file upload
 */
export function createMockFileUpload(filename: string, type: string = 'image/jpeg') {
  const file = new File(['mock file content'], filename, { type });

  // Add mock properties
  Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB

  return file;
}

/**
 * Create mock drag and drop event
 */
export function createMockDragEvent(type: string, files: File[] = []) {
  const event = new Event(type) as any;

  event.dataTransfer = {
    files,
    types: files.length > 0 ? ['Files'] : [],
    getData: vi.fn(),
    setData: vi.fn(),
    clearData: vi.fn(),
    dropEffect: 'copy',
    effectAllowed: 'all',
  };

  event.preventDefault = vi.fn();
  event.stopPropagation = vi.fn();

  return event;
}

/**
 * Create mock canvas context
 */
export function createMockCanvasContext() {
  return {
    canvas: {
      width: 800,
      height: 600,
      getContext: vi.fn(),
    },

    // 2D context methods
    clearRect: vi.fn(),
    fillRect: vi.fn(),
    strokeRect: vi.fn(),
    beginPath: vi.fn(),
    closePath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    arc: vi.fn(),
    fill: vi.fn(),
    stroke: vi.fn(),
    drawImage: vi.fn(),
    getImageData: vi.fn(),
    putImageData: vi.fn(),

    // Properties
    fillStyle: '#000000',
    strokeStyle: '#000000',
    lineWidth: 1,
    font: '10px sans-serif',
    textAlign: 'start',
    textBaseline: 'alphabetic',

    // Transform methods
    save: vi.fn(),
    restore: vi.fn(),
    scale: vi.fn(),
    rotate: vi.fn(),
    translate: vi.fn(),
    transform: vi.fn(),
    setTransform: vi.fn(),
    resetTransform: vi.fn(),
  };
}

/**
 * Wait for Vue component to update
 */
export async function waitForComponentUpdate(component: any, timeout: number = 1000) {
  if (component.$nextTick) {
    await component.$nextTick();
  } else {
    // Fallback for non-Vue components
    await new Promise(resolve => setTimeout(resolve, 10));
  }
}

/**
 * Simulate user interaction
 */
export function simulateUserInteraction(
  element: HTMLElement,
  eventType: string,
  options: any = {},
) {
  const event = new Event(eventType, { bubbles: true, cancelable: true, ...options });

  // Add common event properties
  Object.assign(event, {
    clientX: options.clientX || 100,
    clientY: options.clientY || 100,
    button: options.button || 0,
    buttons: options.buttons || 1,
    ctrlKey: options.ctrlKey || false,
    shiftKey: options.shiftKey || false,
    altKey: options.altKey || false,
    metaKey: options.metaKey || false,
  });

  element.dispatchEvent(event);
  return event;
}

/**
 * Create test environment setup
 */
export function setupTestEnvironment() {
  // Mock global objects
  global.URL =
    global.URL ||
    class URL {
      constructor(url: string) {
        this.href = url;
      }
      href: string;
    };

  global.File =
    global.File ||
    class File {
      constructor(bits: any[], name: string, options: any = {}) {
        this.name = name;
        this.type = options.type || '';
        this.size = bits.reduce((size, bit) => size + (bit.length || 0), 0);
      }
      name: string;
      type: string;
      size: number;
    };

  // Mock canvas
  global.HTMLCanvasElement =
    global.HTMLCanvasElement ||
    class HTMLCanvasElement {
      getContext() {
        return createMockCanvasContext();
      }
      width = 800;
      height = 600;
    };

  // Mock image
  global.Image =
    global.Image ||
    class Image {
      onload: (() => void) | null = null;
      onerror: (() => void) | null = null;
      src: string = '';
      width: number = 0;
      height: number = 0;

      set src(value: string) {
        setTimeout(() => {
          this.width = 100;
          this.height = 100;
          if (this.onload) this.onload();
        }, 0);
      }
    };
}

/**
 * Generate mock vendor data
 */
export function generateMockVendor(overrides: any = {}) {
  return {
    id: 'vendor-123',
    name: 'Test Vendor',
    email: '<EMAIL>',
    company: 'Test Company',
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  };
}

/**
 * Generate mock product data
 */
export function generateMockProduct(overrides: any = {}) {
  return {
    id: 'product-123',
    vendor_id: 'vendor-123',
    name: 'Test Product',
    description: 'A test product for unit testing',
    category: 'furniture',
    price: 99.99,
    currency: 'USD',
    dimensions: {
      width: 100,
      height: 50,
      depth: 80,
      unit: 'cm',
    },
    materials: ['wood', 'metal'],
    colors: ['brown', 'black'],
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  };
}

/**
 * Generate mock material data
 */
export function generateMockMaterial(overrides: any = {}) {
  return {
    id: 'material-123',
    name: 'Test Material',
    type: 'fabric',
    color: '#FF0000',
    texture_url: 'https://example.com/texture.jpg',
    normal_map_url: 'https://example.com/normal.jpg',
    properties: {
      roughness: 0.5,
      metallic: 0.0,
      durability: 'high',
      maintenance: 'low',
      eco_friendly: true,
    },
    vendor_id: 'vendor-123',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  };
}

/**
 * Generate mock animation data
 */
export function generateMockAnimation(overrides: any = {}) {
  return {
    id: 'animation-123',
    name: 'Test Animation',
    type: 'rotation',
    duration: 5000,
    loop: true,
    target_object: 'product-123',
    keyframes: [
      { time: 0, rotation: { x: 0, y: 0, z: 0 } },
      { time: 2500, rotation: { x: 0, y: 180, z: 0 } },
      { time: 5000, rotation: { x: 0, y: 360, z: 0 } },
    ],
    easing: 'linear',
    vendor_id: 'vendor-123',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  };
}

export default {
  createMockEditorState,
  createMockAsset,
  createMockScene,
  createMockVueComponent,
  createMockApiClient,
  createMockApiResponses,
  createMockFileUpload,
  createMockDragEvent,
  createMockCanvasContext,
  waitForComponentUpdate,
  simulateUserInteraction,
  setupTestEnvironment,
  generateMockVendor,
  generateMockProduct,
  generateMockMaterial,
  generateMockAnimation,
};
